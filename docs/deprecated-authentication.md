# DEprecated Authentication System Analysis (Better-Auth)

## Overview

The deprecated authentication system is built using **Better-Auth** library with a Next.js 15 frontend and Hono.js backend API. The system implements a comprehensive authentication flow including user registration, email verification, password reset, organization invitations, and multi-step onboarding. This is being deprecated in favour of Auth.js v5

## Authentication Architecture

### Core Libraries

- **Better-Auth**: Primary authentication library handling sessions, providers, and user management
- **Prisma**: Database ORM with MySQL (PlanetScale)
- **React Hook Form + Zod**: Form validation and handling
- **Resend**: Email delivery service
- **React Email**: Email template system

### Database Schema

- Users table with onboarding status tracking (`incomplete` | `workspace` | `invite` | `complete`)
- Organizations table with role-based access control
- Members table linking users to organizations with roles
- Invitations table for managing organization invites
- Sessions table for authentication session management

## Route Analysis

### `/sign-in`

**File**: `apps/dashboard/src/app/(auth)/sign-in/page.tsx`

#### Authentication Protection

- **Pre-auth Check**: Uses `getAuthenticatedUserRedirect()` to determine if user already has valid session
- **Redirect Logic**: If user is already authenticated, redirects to appropriate destination (workspace, onboarding, etc.)

#### Form Handling

**Component**: `SignInForm` (`apps/dashboard/src/features/auth/components/sign-in-form.tsx`)

- **Validation**: Uses Zod schema requiring email and password (min 8 chars)
- **Better-Auth Integration**: Uses `authClient.signIn.email()` method
- **Session Storage**: Checks for `postVerificationRedirect` and `pendingInvitationId` for intelligent routing

#### Redirections

1. **Post-verification redirect**: Priority given to stored verification redirect URL
2. **Pending invitation**: Redirects to accept invitation flow if invitation ID exists
3. **Default**: Redirects to root `/` which then uses smart routing logic

#### Error Handling

- Displays Better-Auth error messages
- Clears errors on user input
- Handles rate limiting and various auth failure scenarios

### `/sign-up`

**File**: `apps/dashboard/src/app/(auth)/sign-up/page.tsx`

#### Authentication Protection

- Same pre-auth check as sign-in using `getAuthenticatedUserRedirect()`
- Redirects authenticated users to appropriate destination

#### Form Handling

**Component**: `SignUpForm` (`apps/dashboard/src/features/auth/components/sign-up-form.tsx`)

- **Validation**: Requires firstName, lastName, email, password (min 8 chars)
- **Invitation Context**: Pre-fills email if user has `pendingInvitationEmail` in session storage
- **Better-Auth Integration**: Uses `authClient.signUp.email()` with combined name field

#### Redirections

- **Success**: Always redirects to `/verify` for email verification
- **Email Pre-fill**: For invitation flows, email field is disabled and pre-populated

#### Error Handling

- Sanitizes error messages for security
- Handles duplicate account, validation, and rate limiting errors
- Provides user-friendly error messages

### `/reset-password`

**File**: `apps/dashboard/src/app/(auth)/reset-password/page.tsx`

#### Authentication Protection

- Uses `getAuthenticatedUserRedirect()` to check if user is already authenticated
- Redirects authenticated users away from reset flow

#### Form Handling

**Component**: `ResetPasswordForm` (`apps/dashboard/src/features/auth/components/reset-password-form.tsx`)

- **Validation**: Simple email validation using Zod
- **Better-Auth Integration**: Uses `authClient.forgetPassword()` method
- **Success State**: Shows confirmation message after email sent

#### Email Integration

- Backend sends password reset email via Better-Auth configuration
- Custom email template using React Email
- Reset link points to `/reset-password/new?token={token}`

### `/reset-password/new`

**File**: `apps/dashboard/src/app/(auth)/reset-password/new/page.tsx`

#### Authentication Protection

- Same auth check - redirects if user already authenticated
- Dynamic route forced with `dynamic = "force-dynamic"`

#### Form Handling

**Component**: `NewPasswordForm` (`apps/dashboard/src/features/auth/components/new-password-form.tsx`)

- **Token Validation**: Extracts token from URL search params
- **Password Confirmation**: Validates password match using Zod refinement
- **Better-Auth Integration**: Uses `authClient.resetPassword()` with token

#### Security

- Validates token presence before allowing form submission
- Redirects to sign-in upon successful password reset
- Shows error states for invalid/expired tokens

### `/verify`

**File**: `apps/dashboard/src/app/(auth)/verify/page.tsx`

#### Authentication Protection

- **Requires Auth**: Uses `requireAuth()` to ensure user is logged in
- **Email Status Check**: Redirects verified users to appropriate destination using `getAuthenticatedUserRedirect()`

#### Form Handling

**Component**: `VerifyForm` (`apps/dashboard/src/features/auth/components/verify-form.tsx`)

- **Session Integration**: Uses Better-Auth `useSession()` hook to get current user
- **Email Provider Links**: Quick links to Gmail, Apple Mail, Outlook, Yahoo
- **Resend Functionality**: `authClient.sendVerificationEmail()` for resending verification

#### User Experience

- Shows user's email address from session
- Loading states with skeletons
- Success confirmation after resending email

### `/verify-email/[token]`

**File**: `apps/dashboard/src/app/(auth)/verify-email/[token]/page.tsx`

#### Authentication Protection

- **Public Route**: No authentication required (can be accessed via email link)
- Uses dynamic token parameter

#### Processing Logic

**Component**: `EmailVerificationHandler` (`apps/dashboard/src/features/auth/components/email-verification-handler.tsx`)

- **Token Verification**: Uses `authClient.verifyEmail()` with token
- **Invitation Handling**: Checks for `pendingInvitationId` in session storage
- **Onboarding Status**: Updates to 'workspace' for regular users, skips for invited users

#### Redirections

1. **Invited Users**: Redirects to `/onboarding/accept-invitation/{id}`
2. **Regular Users**: Redirects to `/onboarding/workspace`
3. **Error States**: Shows error message with retry options

#### API Integration

- Updates onboarding status via `updateOnboardingStatus()` API call
- Handles session timing issues with retry logic

### `/verify-email-success`

**File**: Route appears to exist in folder structure but no page.tsx found

- May be an unused/placeholder route

## Onboarding Flow Analysis

### `/onboarding/(steps)/workspace`

**File**: `apps/dashboard/src/app/onboarding/(steps)/workspace/page.tsx`

#### Authentication Protection

- **Requires Verified User**: Uses `requireVerifiedUser()` (authenticated + email verified)
- **Completion Check**: Redirects users with `complete` onboarding status to home
- **Organization Check**: Redirects users who already have organizations to home

#### Form Handling

**Component**: `WorkspaceOnboardingForm` (`apps/dashboard/src/features/auth/components/workspace-onboarding-form.tsx`)

- **Validation**: Workspace name (required) and slug (3-48 chars, lowercase, hyphens allowed)
- **Slug Availability**: Real-time check via `authClient.organization.checkSlug()`
- **Organization Creation**: Uses `authClient.organization.create()`

#### Post-Creation Actions

1. **Set Active Organization**: `authClient.organization.setActive()`
2. **Update Onboarding Status**: Sets to 'invite' status
3. **Set Default Workspace**: Updates user's default workspace preference
4. **Redirect**: Goes to `/onboarding/invite`

#### Error Handling

- Comprehensive error handling for various failure scenarios
- User-friendly messages for common issues (slug taken, validation errors)
- Status-code specific error responses

### `/onboarding/(steps)/invite`

**File**: `apps/dashboard/src/app/onboarding/(steps)/invite/page.tsx`

#### Authentication Protection

- **Requires Verified User**: Must be authenticated and email verified
- **Workspace Check**: Redirects to workspace creation if no organizations
- **Completion Check**: Redirects if onboarding already complete

#### Form Handling

**Component**: `InviteOnboardingForm` (`apps/dashboard/src/features/auth/components/invite-onboarding-form.tsx`)

- **Three Invitation Slots**: Fixed array of 3 email/role pairs
- **Role Selection**: Member, Admin, or Owner roles
- **Validation**: Requires at least one valid email address
- **Batch Processing**: Sends all invitations in parallel using `Promise.all()`

#### Organization Integration

- **Active Organization**: Uses Better-Auth `useActiveOrganization()` hook
- **Invitation API**: Uses `authClient.organization.inviteMember()`
- **Completion**: Updates onboarding status to 'complete'

#### User Experience

- **Skip Option**: Users can skip invitation step
- **Completion Loader**: Shows loading state before redirect
- **Error Handling**: Handles duplicate members, rate limits, invalid emails

### `/onboarding/(steps)/accept-invitation/[id]`

**File**: `apps/dashboard/src/app/onboarding/(steps)/accept-invitation/[id]/page.tsx`

#### Authentication Protection

- **Flexible Auth**: Works for both authenticated and unauthenticated users
- **Session Optional**: Uses Better-Auth `auth.api.getSession()` to check current user
- **Invitation Validation**: Server-side validation via backend API

#### Processing Logic

**Component**: `InvitationHandler` (`apps/dashboard/src/features/auth/components/invitation-handler.tsx`)

**For Unauthenticated Users**:

1. Stores invitation ID in session storage
2. Fetches invitation details to get email
3. Stores invitation email in session storage
4. Redirects to `/sign-up?invitation={id}` with pre-filled email

**For Authenticated Users**:

1. Fetches invitation details via `authClient.organization.getInvitation()`
2. Validates invitation status (must be 'pending')
3. Validates email match between invitation and logged-in user
4. Shows accept/decline options

#### Invitation Actions

- **Accept**: Uses `authClient.organization.acceptInvitation()`
  - Updates onboarding status to 'complete'
  - Sets organization as default workspace
  - Redirects to workspace home page
- **Decline**: Uses `authClient.organization.rejectInvitation()`
  - Clears session storage
  - Redirects to external marketing site

#### Error Handling

- Email mismatch errors
- Invalid/expired invitation handling
- Sign-out option for wrong email scenarios

## Authentication Protection Mechanisms

### Middleware

**File**: `apps/dashboard/src/middleware.ts`

- **IP-based Access Control**: Validates client IP against whitelist
- **Environment Variables**: Requires `ALLOWED_IPS` and `MARKETING_URL`
- **Redirect Logic**: Redirects unauthorized IPs to marketing site
- **Error Handling**: Fail-safe approach allows access on errors

### Data Access Layer (DAL)

**File**: `apps/dashboard/src/features/auth/dal/authentication.ts`

#### Core Authentication Functions

**`requireAuth()`**

- Uses Better-Auth `auth.api.getSession()` with request headers
- Redirects to `/sign-in` if no valid session
- Returns session object for authenticated users

**`requireVerifiedUser()`**

- Builds on `requireAuth()`
- Checks `emailVerified` status
- Redirects to `/verify` if email not verified

**`requireOnboardedUser()`**

- Builds on `requireVerifiedUser()`
- Checks `onboardingStatus` field
- Redirects based on current status:
  - `incomplete` → `/onboarding/workspace`
  - `workspace` → `/onboarding/invite`
  - `invite` → `/onboarding/invite`

**`requireWorkspaceAccess(slug)`**

- Builds on `requireOnboardedUser()`
- Fetches user's organizations
- Validates access to specific workspace
- Returns session, organizations, and workspace data

#### Smart Redirection Logic

**`getAuthenticatedUserRedirect()`**

- **Single API Call**: Optimized for performance, used in auth pages
- **Decision Tree**:
  1. No session → `/sign-in`
  2. Unverified email → `/verify`
  3. Incomplete onboarding → appropriate onboarding step
  4. Complete onboarding → workspace or home page
- **Default Workspace**: Uses user's `defaultWorkspace` setting
- **Fallback**: Redirects to first organization if no default set

#### Organization Management

**`getUserOrganizations()`**

- **Caching**: Uses React `cache()` for request deduplication
- **Rate Limiting**: Graceful handling of 429 responses
- **Error Handling**: Returns empty array on failures
- **API Integration**: Calls backend `/api/v1/profile/organizations`

**`requireWorkspaceRole(slug, role)`**

- **Role Hierarchy**: member < admin < owner
- **Permission Checking**: Validates sufficient permissions
- **Access Control**: Redirects unauthorized users

### Dashboard Protection

**File**: `apps/dashboard/src/app/(dashboard)/[slug]/layout.tsx`

- **Layout Wrapper**: `DashboardLayoutWrapper` component
- **Implicit Protection**: Relies on route-level auth checks
- **No Direct Auth**: Layout itself doesn't implement auth protection

### Root Page Redirection

**File**: `apps/dashboard/src/app/page.tsx`

- **Smart Router**: Uses `getAuthenticatedUserRedirect()` for all users
- **Single Redirect**: Always redirects, never renders content
- **Centralized Logic**: Consolidates all routing decisions

## Session and State Management

### Better-Auth Configuration

**File**: `packages/authentication/src/lib/auth.ts`

#### Session Settings

- **Expiration**: 7 days (60 _ 60 _ 24 \* 7 seconds)
- **Update Age**: 24 hours (60 _ 60 _ 24 seconds)
- **Cookie Cache**: 5-minute client-side caching
- **Cross-Subdomain**: Enabled for production (`.centaly.com`)

#### Database Integration

- **Prisma Adapter**: Uses MySQL with PlanetScale
- **Session Hooks**: Automatically sets `activeOrganizationId` on session creation
- **Organization Assignment**: Uses user's first (oldest) organization membership

#### Email Configuration

- **Email Provider**: Resend service
- **Custom Templates**: React Email components
- **Password Reset**: Custom reset flow with frontend redirect
- **Email Verification**: Custom verification flow with frontend redirect
- **Invitations**: Integrated with organization plugin

#### Plugins

- **Admin Plugin**: Defines admin roles (admin, owner)
- **Organization Plugin**: Full organization management with invitations
- **Custom Session Plugin**: Enriches session with onboarding status

### Client Configuration

**File**: `packages/authentication/src/lib/auth-client.ts`

#### Client Setup

- **Base URL**: Uses `NEXT_PUBLIC_API_URL` environment variable
- **Plugins**: Organization, two-factor, admin, multi-session, custom session
- **Credentials**: Include cookies for cross-origin requests
- **Error Handling**: Rate limiting awareness (429 status)

#### Two-Factor Setup

- **Redirect Handling**: Redirects to `/two-factor` route
- **Client-Side Logic**: Handles 2FA flow (though not actively used)

### Session Storage Usage

#### Invitation Flow State

- **`pendingInvitationId`**: Stores invitation ID during sign-up process
- **`pendingInvitationEmail`**: Pre-fills email during invitation sign-up
- **Cleanup**: Cleared after successful invitation acceptance/rejection

#### Verification Flow State

- **`postVerificationRedirect`**: Stores intended destination after email verification
- **Usage**: Primarily for invitation flows requiring email verification
- **Cleanup**: Cleared after successful sign-in

## Email System Integration

### Email Templates

**Location**: `packages/email/src/templates/`

- **Verification Email**: `verify-email.tsx`
- **Password Reset**: `reset-password-email.tsx`
- **Invitation Email**: `invite-user-email.tsx`
- **Welcome Email**: Sent after successful sign-in

### Email Sending

**Location**: `packages/email/src/senders/`

- **Resend Integration**: Centralized email service configuration
- **Template Rendering**: React Email to HTML conversion
- **Error Handling**: Comprehensive error logging and fallbacks

### Backend Email Configuration

**Location**: Better-Auth configuration in `packages/authentication/src/lib/auth.ts`

- **Environment-Aware URLs**: Different URLs for development/production
- **Custom Link Generation**: Frontend-controlled redirect URLs
- **Email Verification**: Custom handler with frontend redirect
- **Password Reset**: Custom handler with token extraction

## API Integration

### Backend Routes

**Location**: `apps/backend/src/routes/`

#### User Management (`users.ts`)

- **GET `/api/v1/profile`**: Get current user profile (protected)
- **PATCH `/api/v1/profile/onboarding-status`**: Update onboarding status
- **PATCH `/api/v1/profile/default-workspace`**: Update default workspace setting

#### Organization Management (`organizations.ts`)

- **GET `/api/v1/organizations/{slug}`**: Get organization details with user role
- **GET `/api/v1/profile/organizations`**: List user's organizations

#### Invitation Management (`invitations.ts`)

- **GET `/api/v1/invitations/{id}/validate`**: Validate invitation (public)
- **POST `/api/v1/invitations/{id}/accept`**: Accept invitation (protected)
- **POST `/api/v1/invitations/{id}/reject`**: Reject invitation (protected)

### Frontend API Clients

**Location**: `apps/dashboard/src/lib/api/`

- **Onboarding API**: Helper functions for onboarding status updates
- **Workspace API**: Helper functions for workspace management
- **Member API**: Helper functions for member management

### Better-Auth API Integration

- **Session Management**: Automatic session handling with cookies
- **Organization Operations**: Full CRUD operations for organizations
- **Invitation System**: Complete invitation lifecycle management
- **User Management**: Profile updates and user settings

## Security Considerations

### Authentication Security

- **Session-Based**: Database-stored sessions with configurable expiration
- **CSRF Protection**: Built-in CSRF token handling
- **Secure Cookies**: HTTPOnly, Secure, SameSite configuration
- **Rate Limiting**: Built-in protection against brute force attacks

### Authorization Security

- **Role-Based Access**: Organization-level permissions (viewer, contributor, admin, owner)
- **Route Protection**: Multiple layers of authentication checking
- **Workspace Isolation**: Users can only access organizations they're members of
- **Invitation Validation**: Email-based invitation validation

### Data Security

- **Environment Variables**: Sensitive configuration externalized
- **IP Whitelisting**: Network-level access control
- **Input Validation**: Zod schema validation throughout
- **Error Sanitization**: User-friendly error messages without internal details

### Email Security

- **Token-Based Verification**: Secure email verification tokens
- **Expiration Handling**: Time-limited verification and reset tokens
- **Domain Validation**: Proper email domain verification
- **Template Security**: Safe email template rendering

## Migration Considerations

### Session Management

- Current system uses Better-Auth with database sessions
- Session duration: 7 days with 24-hour refresh
- Cookie configuration includes cross-subdomain support

### User Model

- User IDs are ULIDs (not UUIDs)
- Onboarding status enum: `incomplete` | `workspace` | `invite` | `complete`
- Default workspace preference stored per user

### Organization Model

- Organizations have slugs for URL routing
- Role hierarchy: viewer < contributor < admin < owner
- Organization logos stored in S3 with presigned URLs

### Email Integration

- Uses Resend service for email delivery
- Custom React Email templates
- Environment-aware URL generation for links

### Database Schema

- Prisma ORM with MySQL (PlanetScale)
- Better-Auth manages core auth tables
- Custom tables for organizations, members, invitations

### API Architecture

- Hono.js backend with OpenAPI specification
- Type-safe request/response validation
- Comprehensive error handling and status codes
